import '../../shared/models/product.dart';

class MockData {
  static List<Category> get categories => [
    Category(
      id: '1',
      name: 'Electronics',
      image: 'assets/images/category_electronics.png',
      icon: '📱',
      productCount: 156,
    ),
    Category(
      id: '2',
      name: 'Fashion',
      image: 'assets/images/category_fashion.png',
      icon: '👕',
      productCount: 234,
    ),
    Category(
      id: '3',
      name: 'Home & Garden',
      image: 'assets/images/category_home.png',
      icon: '🏠',
      productCount: 89,
    ),
    Category(
      id: '4',
      name: 'Sports',
      image: 'assets/images/category_sports.png',
      icon: '⚽',
      productCount: 67,
    ),
    Category(
      id: '5',
      name: 'Beauty',
      image: 'assets/images/category_beauty.png',
      icon: '💄',
      productCount: 123,
    ),
    Category(
      id: '6',
      name: 'Books',
      image: 'assets/images/category_books.png',
      icon: '📚',
      productCount: 45,
    ),
  ];

  static List<Product> get products => [
    Product(
      id: '1',
      name: 'iPhone 15 Pro Max',
      description: 'The most advanced iPhone ever with titanium design, A17 Pro chip, and professional camera system.',
      price: 1199.99,
      originalPrice: 1299.99,
      images: [
        'assets/images/iphone_1.png',
        'assets/images/iphone_2.png',
        'assets/images/iphone_3.png',
      ],
      category: 'Electronics',
      rating: 4.8,
      reviewCount: 1247,
      isInStock: true,
      colors: ['Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium'],
      sizes: ['128GB', '256GB', '512GB', '1TB'],
      brand: 'Apple',
      isFeatured: true,
      isOnSale: true,
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
    Product(
      id: '2',
      name: 'Samsung Galaxy S24 Ultra',
      description: 'Premium Android smartphone with S Pen, advanced AI features, and exceptional camera quality.',
      price: 1099.99,
      images: [
        'assets/images/samsung_1.png',
        'assets/images/samsung_2.png',
      ],
      category: 'Electronics',
      rating: 4.7,
      reviewCount: 892,
      isInStock: true,
      colors: ['Titanium Black', 'Titanium Gray', 'Titanium Violet'],
      sizes: ['256GB', '512GB', '1TB'],
      brand: 'Samsung',
      isFeatured: true,
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    Product(
      id: '3',
      name: 'Nike Air Max 270',
      description: 'Comfortable running shoes with Max Air unit for exceptional cushioning and style.',
      price: 149.99,
      originalPrice: 179.99,
      images: [
        'assets/images/nike_1.png',
        'assets/images/nike_2.png',
        'assets/images/nike_3.png',
      ],
      category: 'Sports',
      rating: 4.6,
      reviewCount: 567,
      isInStock: true,
      colors: ['Black/White', 'Blue/White', 'Red/Black'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      brand: 'Nike',
      isOnSale: true,
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
    ),
    Product(
      id: '4',
      name: 'MacBook Pro 14"',
      description: 'Powerful laptop with M3 chip, Liquid Retina XDR display, and all-day battery life.',
      price: 1999.99,
      images: [
        'assets/images/macbook_1.png',
        'assets/images/macbook_2.png',
      ],
      category: 'Electronics',
      rating: 4.9,
      reviewCount: 423,
      isInStock: true,
      colors: ['Space Gray', 'Silver'],
      sizes: ['512GB', '1TB', '2TB'],
      brand: 'Apple',
      isFeatured: true,
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    Product(
      id: '5',
      name: 'Levi\'s 501 Original Jeans',
      description: 'Classic straight-leg jeans with authentic fit and timeless style.',
      price: 89.99,
      originalPrice: 109.99,
      images: [
        'assets/images/levis_1.png',
        'assets/images/levis_2.png',
      ],
      category: 'Fashion',
      rating: 4.4,
      reviewCount: 789,
      isInStock: true,
      colors: ['Dark Blue', 'Light Blue', 'Black'],
      sizes: ['28', '30', '32', '34', '36', '38'],
      brand: 'Levi\'s',
      isOnSale: true,
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
    ),
    Product(
      id: '6',
      name: 'Sony WH-1000XM5',
      description: 'Industry-leading noise canceling headphones with exceptional sound quality.',
      price: 399.99,
      images: [
        'assets/images/sony_1.png',
        'assets/images/sony_2.png',
      ],
      category: 'Electronics',
      rating: 4.8,
      reviewCount: 1156,
      isInStock: true,
      colors: ['Black', 'Silver'],
      sizes: ['One Size'],
      brand: 'Sony',
      isFeatured: true,
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    Product(
      id: '7',
      name: 'Adidas Ultraboost 22',
      description: 'Premium running shoes with responsive cushioning and energy return.',
      price: 189.99,
      images: [
        'assets/images/adidas_1.png',
        'assets/images/adidas_2.png',
      ],
      category: 'Sports',
      rating: 4.5,
      reviewCount: 334,
      isInStock: false,
      colors: ['Core Black', 'Cloud White', 'Solar Red'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      brand: 'Adidas',
      createdAt: DateTime.now().subtract(const Duration(days: 4)),
    ),
    Product(
      id: '8',
      name: 'The Psychology of Money',
      description: 'Timeless lessons on wealth, greed, and happiness by Morgan Housel.',
      price: 16.99,
      originalPrice: 24.99,
      images: [
        'assets/images/book_1.png',
      ],
      category: 'Books',
      rating: 4.7,
      reviewCount: 2341,
      isInStock: true,
      colors: ['Paperback', 'Hardcover'],
      sizes: ['Standard'],
      brand: 'Harriman House',
      isOnSale: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
    ),
  ];

  static List<Product> get featuredProducts => 
      products.where((product) => product.isFeatured).toList();

  static List<Product> get saleProducts => 
      products.where((product) => product.isOnSale).toList();

  static List<Product> get newProducts => 
      products..sort((a, b) => b.createdAt.compareTo(a.createdAt));

  static List<Product> getProductsByCategory(String category) =>
      products.where((product) => product.category == category).toList();

  static Product? getProductById(String id) {
    try {
      return products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }
}
