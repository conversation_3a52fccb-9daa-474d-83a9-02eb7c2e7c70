# 📚 Technical Documentation

## 🏗️ **Architecture Overview**

This Flutter ecommerce application follows a **feature-based architecture** with clean separation of concerns and modular design patterns.

### **Directory Structure**

```
lib/
├── core/                   # Core application logic
│   ├── data/              # Mock data and data sources
│   │   └── mock_data.dart # Sample products, categories, users
│   ├── navigation/        # App routing and navigation
│   │   ├── app_router.dart      # GoRouter configuration
│   │   └── main_navigation.dart # Bottom navigation
│   └── theme/             # Design system
│       ├── app_colors.dart      # Color palette
│       └── app_text_styles.dart # Typography
├── features/              # Feature modules
│   ├── home/             # Home screen feature
│   │   └── presentation/
│   │       └── home_screen.dart
│   ├── search/           # Search functionality
│   │   └── presentation/
│   │       └── search_screen.dart
│   ├── cart/             # Shopping cart
│   │   ├── models/
│   │   │   └── cart_item.dart
│   │   └── presentation/
│   │       └── cart_screen.dart
│   └── profile/          # User profile
│       └── presentation/
│           └── profile_screen.dart
├── shared/               # Shared components
│   ├── models/          # Data models
│   │   └── product.dart # Product and Category models
│   └── widgets/         # Reusable UI components
│       ├── product_card.dart
│       └── category_card.dart
└── main.dart            # App entry point
```

## 🎨 **Design System**

### **Color Scheme**
The app uses a unified color palette defined in `app_colors.dart`:

```dart
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF007AFF);      // Blue
  static const Color secondary = Color(0xFF5856D6);    // Purple
  
  // Background Colors
  static const Color background = Color(0xFFFAFAFA);   // Light Gray
  static const Color surface = Color(0xFFFFFFFF);      // White
  static const Color card = Color(0xFFFFFFFF);         // White
  
  // Text Colors
  static const Color textPrimary = Color(0xFF1D1D1F);  // Dark Gray
  static const Color textSecondary = Color(0xFF8E8E93); // Medium Gray
  static const Color textHint = Color(0xFFC7C7CC);     // Light Gray
  
  // Status Colors
  static const Color success = Color(0xFF34C759);      // Green
  static const Color warning = Color(0xFFFF9500);      // Orange
  static const Color error = Color(0xFFFF3B30);        // Red
  
  // Border Colors
  static const Color border = Color(0xFFE5E5EA);       // Light Border
  static const Color borderLight = Color(0xFFF2F2F7);  // Very Light Border
}
```

### **Typography System**
Consistent text styles defined in `app_text_styles.dart`:

```dart
class AppTextStyles {
  // Headlines
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );
  
  // Titles
  static const TextStyle titleLarge = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle titleMedium = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
  );
  
  // Body Text
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 17,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 15,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
  );
  
  // Labels
  static const TextStyle labelLarge = TextStyle(
    fontSize: 17,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
  );
}
```

## 🧩 **Core Components**

### **Navigation System**
The app uses **GoRouter** for declarative routing:

```dart
class AppRouter {
  static const String home = '/home';
  static const String search = '/search';
  static const String cart = '/cart';
  static const String profile = '/profile';
  
  static final GoRouter router = GoRouter(
    initialLocation: home,
    routes: [
      ShellRoute(
        builder: (context, state, child) => MainNavigation(child: child),
        routes: [
          GoRoute(
            path: home,
            name: 'home',
            builder: (context, state) => const HomeScreen(),
          ),
          // ... other routes
        ],
      ),
    ],
  );
}
```

### **Data Models**

#### **Product Model**
```dart
class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final double? originalPrice;
  final List<String> images;
  final String category;
  final double rating;
  final int reviewCount;
  final bool isInStock;
  final List<String> colors;
  final List<String> sizes;
  final String brand;
  final bool isFeatured;
  final bool isOnSale;
  final DateTime createdAt;
  
  // Computed properties
  double get discountPercentage {
    if (originalPrice == null || originalPrice! <= price) return 0;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }
  
  bool get hasDiscount => originalPrice != null && originalPrice! > price;
}
```

#### **Cart Item Model**
```dart
class CartItem {
  final String id;
  final Product product;
  final int quantity;
  final String? selectedSize;
  final String? selectedColor;
  final DateTime addedAt;
  
  double get totalPrice => product.price * quantity;
  
  CartItem copyWith({
    String? id,
    Product? product,
    int? quantity,
    String? selectedSize,
    String? selectedColor,
    DateTime? addedAt,
  }) {
    return CartItem(
      id: id ?? this.id,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      selectedSize: selectedSize ?? this.selectedSize,
      selectedColor: selectedColor ?? this.selectedColor,
      addedAt: addedAt ?? this.addedAt,
    );
  }
}
```

## 📱 **Screen Components**

### **Home Screen Features**
- **Unified Background**: Single color (`AppColors.background`) throughout
- **Smart Header**: 90% search bar + 10% notification icon layout
- **Product Sections**: Featured, Flash Sale, New Arrivals
- **Category Navigation**: Horizontal scrollable category cards

### **Search Screen Features**
- **Auto-focus**: Search field automatically focused on screen load
- **Search History**: Stores up to 10 recent searches
- **Trending Searches**: Predefined popular search terms
- **Category Shortcuts**: Quick access to product categories
- **Real-time Results**: Live search with product filtering

### **Cart Screen Features**
- **Quantity Management**: +/- controls with validation
- **Variant Display**: Size and color chips
- **Pricing Logic**: 
  - Subtotal calculation
  - 8% tax rate
  - Free shipping over $100
  - Promo code support
- **Empty State**: Helpful messaging with call-to-action

### **Profile Screen Features**
- **User Stats**: Orders, spending, loyalty points
- **Menu Sections**: Account, Orders, Preferences, Support
- **Interactive Elements**: Dark mode toggle, logout confirmation
- **Professional Layout**: Consistent spacing and typography

## 🔧 **Customization Guide**

### **Adding New Products**
Edit `lib/core/data/mock_data.dart`:

```dart
static List<Product> get products => [
  Product(
    id: 'new_product_id',
    name: 'Your Product Name',
    description: 'Product description',
    price: 99.99,
    originalPrice: 129.99, // Optional for discounts
    images: ['https://your-image-url.com/image.jpg'],
    category: 'Electronics',
    rating: 4.5,
    reviewCount: 123,
    isInStock: true,
    colors: ['Black', 'White'],
    sizes: ['S', 'M', 'L'],
    brand: 'Your Brand',
    isFeatured: true,
    isOnSale: true,
    createdAt: DateTime.now(),
  ),
  // ... existing products
];
```

### **Adding New Categories**
```dart
static List<Category> get categories => [
  Category(
    id: 'new_category_id',
    name: 'New Category',
    image: 'assets/images/category_image.png',
    icon: '🆕', // Emoji icon
    productCount: 50,
  ),
  // ... existing categories
];
```

### **Customizing Colors**
Modify `lib/core/theme/app_colors.dart`:

```dart
class AppColors {
  static const Color primary = Color(0xFFYourColor); // Your brand color
  static const Color background = Color(0xFFYourBG); // Your background
  // ... other colors
}
```

### **Adding New Screens**
1. Create feature folder: `lib/features/your_feature/`
2. Add presentation folder: `presentation/`
3. Create screen file: `your_screen.dart`
4. Add route to `app_router.dart`
5. Update navigation if needed

## 🚀 **Performance Optimizations**

### **Image Loading**
- Uses `NetworkImage` for remote images
- Implements proper error handling
- Caches images automatically

### **Widget Optimization**
- Uses `const` constructors where possible
- Implements proper `dispose()` methods
- Avoids unnecessary rebuilds

### **Memory Management**
- Proper controller disposal
- Efficient list rendering
- Optimized image loading

## 🧪 **Testing Strategy**

### **Unit Tests**
Test business logic and data models:
```dart
test('Product discount calculation', () {
  final product = Product(
    // ... product data
    price: 80.0,
    originalPrice: 100.0,
  );
  
  expect(product.discountPercentage, 20.0);
  expect(product.hasDiscount, true);
});
```

### **Widget Tests**
Test UI components:
```dart
testWidgets('ProductCard displays product info', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: ProductCard(product: mockProduct),
    ),
  );
  
  expect(find.text(mockProduct.name), findsOneWidget);
  expect(find.text('\$${mockProduct.price}'), findsOneWidget);
});
```

## 🔒 **Security Considerations**

### **Data Validation**
- Input sanitization for search queries
- Price validation for cart items
- User input validation for forms

### **State Management**
- Proper state isolation
- Secure data flow
- No sensitive data in widgets

## 📊 **Analytics Integration**

Ready for analytics integration:
- Screen view tracking
- User interaction events
- Purchase funnel analysis
- Search behavior tracking

## 🌐 **Internationalization**

Prepared for multi-language support:
- Externalized strings
- Locale-aware formatting
- RTL layout support
- Currency formatting

---

This documentation provides a comprehensive overview of the application architecture, components, and customization options. For specific implementation details, refer to the inline code comments and individual file documentation.

**© 2025 AGS Company. All rights reserved.**
**Technical Support: <EMAIL>**
**GitHub: https://github.com/agscompany/ecommerce-flutter-app**
