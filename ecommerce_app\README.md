# 🛒 Modern Ecommerce Flutter App

A complete, professional ecommerce mobile application built with Flutter, featuring modern UI/UX design and comprehensive shopping functionality.

![Flutter](https://img.shields.io/badge/Flutter-02569B?style=for-the-badge&logo=flutter&logoColor=white)
![Dart](https://img.shields.io/badge/Dart-0175C2?style=for-the-badge&logo=dart&logoColor=white)
![License](https://img.shields.io/badge/License-MIT-green.svg?style=for-the-badge)

## 📱 Screenshots

### Home Screen
- Clean, unified background design
- Search bar with notification icon (90%/10% layout)
- Full-width banner carousel
- Category cards with gradient design
- Featured products, flash sales, and new arrivals

### Search Screen
- Auto-focus search functionality
- Recent search history management
- Trending searches with icons
- Popular categories grid
- Real-time search results
- No results state with helpful messaging

### Cart Screen
- Comprehensive cart management
- Quantity controls with +/- buttons
- Product variants (size, color) display
- Promo code functionality
- Smart pricing breakdown (subtotal, shipping, tax)
- Free shipping threshold indicator
- Professional checkout interface

### Profile Screen
- Professional user profile header
- Interactive stats cards (Orders, Spent, Points)
- Comprehensive menu system
- Account, orders, preferences, and support sections
- Dark mode toggle
- Secure logout with confirmation dialog

## ✨ Key Features

### 🏠 **Home Screen**
- **Unified Design**: Single background color throughout for clean appearance
- **Smart Header**: Search bar (90%) + notification icon (10%) layout
- **Product Discovery**: Featured products, flash sales, new arrivals
- **Category Navigation**: Gradient-styled category cards
- **Banner Carousel**: Full-width promotional banners

### 🔍 **Search Screen**
- **Intelligent Search**: Auto-focus with real-time results
- **Search History**: Recent searches with clear functionality
- **Trending Topics**: Popular search terms with trending icons
- **Category Shortcuts**: Quick access to popular categories
- **Results Display**: Grid layout with product cards
- **Empty States**: Helpful no-results messaging

### 🛒 **Cart Screen**
- **Cart Management**: Add, remove, update quantities
- **Product Variants**: Size and color selection display
- **Pricing Logic**: Automatic tax calculation (8%)
- **Shipping Rules**: Free shipping over $100
- **Promo Codes**: Discount code application
- **Checkout Flow**: Professional checkout interface

### 👤 **Profile Screen**
- **User Management**: Profile picture, name, email display
- **Statistics**: Order count, total spent, loyalty points
- **Account Settings**: Personal info, addresses, payment methods
- **Order Management**: History, tracking, returns
- **Preferences**: Notifications, language, dark mode
- **Support**: Help center, contact, legal pages

## 🏗️ **Architecture & Code Quality**

### **Clean Architecture**
- Feature-based folder structure
- Separation of concerns
- Reusable components
- Consistent naming conventions

### **Design System**
- Unified color palette
- Consistent typography
- Reusable UI components
- Professional spacing and layout

### **State Management**
- Stateful widgets for local state
- Ready for advanced state management integration
- Clean data flow patterns

## 🚀 **Getting Started**

### **Prerequisites**
- Flutter SDK (3.0.0 or higher)
- Dart SDK (2.17.0 or higher)
- Android Studio / VS Code
- Git

### **Installation**

1. **Clone the repository**
   ```bash
   git clone https://github.com/agscompany/ecommerce-flutter-app.git
   cd ecommerce-flutter-app/ecommerce_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   flutter run
   ```

### **Platform Support**
- ✅ Android
- ✅ iOS
- ✅ Web
- ✅ Windows
- ✅ macOS
- ✅ Linux

## 📁 **Project Structure**

```
ecommerce_app/
├── lib/
│   ├── core/
│   │   ├── data/           # Mock data and models
│   │   ├── navigation/     # App routing
│   │   └── theme/          # Colors, text styles
│   ├── features/
│   │   ├── home/           # Home screen
│   │   ├── search/         # Search functionality
│   │   ├── cart/           # Shopping cart
│   │   └── profile/        # User profile
│   ├── shared/
│   │   ├── models/         # Data models
│   │   └── widgets/        # Reusable components
│   └── main.dart
├── assets/
│   └── images/             # App images and icons
└── pubspec.yaml
```

## 🎨 **Design System**

### **Color Palette**
- **Primary**: #007AFF (Blue)
- **Background**: #FAFAFA (Light Gray)
- **Surface**: #FFFFFF (White)
- **Text Primary**: #1D1D1F (Dark Gray)
- **Text Secondary**: #8E8E93 (Medium Gray)
- **Success**: #34C759 (Green)
- **Warning**: #FF9500 (Orange)
- **Error**: #FF3B30 (Red)

### **Typography**
- **Title Large**: 28px, Bold
- **Title Medium**: 22px, Bold
- **Body Large**: 17px, Regular
- **Body Medium**: 15px, Regular
- **Body Small**: 13px, Regular

## 🔧 **Customization Guide**

### **Changing Colors**
Edit `lib/core/theme/app_colors.dart`:
```dart
class AppColors {
  static const Color primary = Color(0xFF007AFF); // Change this
  static const Color background = Color(0xFFFAFAFA); // And this
  // ... other colors
}
```

### **Adding New Features**
1. Create feature folder in `lib/features/`
2. Add screen, models, and widgets
3. Update navigation in `lib/core/navigation/`
4. Add routes to `app_router.dart`

### **Modifying Mock Data**
Edit `lib/core/data/mock_data.dart` to change:
- Product information
- Categories
- User data
- Pricing

## 📦 **Dependencies**

### **Core Dependencies**
- `flutter`: SDK
- `go_router`: Navigation
- `cupertino_icons`: iOS-style icons

### **Development Dependencies**
- `flutter_test`: Testing framework
- `flutter_lints`: Code analysis

## 🧪 **Testing**

Run tests with:
```bash
flutter test
```

## 🚀 **Deployment**

### **Android**
```bash
flutter build apk --release
```

### **iOS**
```bash
flutter build ios --release
```

### **Web**
```bash
flutter build web --release
```

## 📈 **Performance**

- **Fast startup time**: Optimized widget tree
- **Smooth animations**: 60fps performance
- **Memory efficient**: Proper widget disposal
- **Network optimized**: Efficient image loading

## 🤝 **Contributing**

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 💬 **Support**

- 📧 Email: <EMAIL>
- 💬 GitHub: [Issues & Support](https://github.com/agscompany/ecommerce-flutter-app/issues)
- 📖 Documentation: [Complete Guides](https://github.com/agscompany/ecommerce-flutter-app/wiki)

## 🙏 **Acknowledgments**

- Flutter team for the amazing framework
- Unsplash for product images
- Material Design for design inspiration
- Community contributors

---

**Built with ❤️ by AGS Company using Flutter**
**© 2025 AGS Company. All rights reserved.**
