# 🚀 Setup Guide

Complete step-by-step guide to set up and run the Modern Ecommerce Flutter App.

## 📋 **Prerequisites**

### **Required Software**
- **Flutter SDK**: 3.0.0 or higher
- **Dart SDK**: 2.17.0 or higher
- **Git**: Latest version
- **Code Editor**: VS Code or Android Studio

### **Platform-Specific Requirements**

#### **For Android Development**
- Android Studio (latest version)
- Android SDK (API level 21 or higher)
- Java Development Kit (JDK) 8 or higher
- Android device or emulator

#### **For iOS Development** (macOS only)
- Xcode (latest version)
- iOS Simulator or physical iOS device
- CocoaPods

#### **For Web Development**
- Chrome browser (for debugging)
- Web server (for deployment)

## 🛠️ **Installation Steps**

### **Step 1: Install Flutter**

#### **Windows**
1. Download Flutter SDK from [flutter.dev](https://flutter.dev)
2. Extract to `C:\flutter`
3. Add `C:\flutter\bin` to your PATH
4. Run `flutter doctor` to verify installation

#### **macOS**
```bash
# Using Homebrew
brew install flutter

# Or download and extract manually
# Add to PATH in ~/.zshrc or ~/.bash_profile
export PATH="$PATH:/path/to/flutter/bin"
```

#### **Linux**
```bash
# Download and extract Flutter
wget https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.x.x-stable.tar.xz
tar xf flutter_linux_3.x.x-stable.tar.xz

# Add to PATH
export PATH="$PATH:`pwd`/flutter/bin"
```

### **Step 2: Verify Flutter Installation**
```bash
flutter doctor
```

Expected output should show:
- ✅ Flutter (Channel stable)
- ✅ Android toolchain
- ✅ Chrome (for web development)
- ✅ VS Code or Android Studio

### **Step 3: Clone the Repository**
```bash
git clone https://github.com/yourusername/ecommerce-flutter-app.git
cd ecommerce-flutter-app/ecommerce_app
```

### **Step 4: Install Dependencies**
```bash
flutter pub get
```

### **Step 5: Run the App**

#### **On Android**
```bash
# List available devices
flutter devices

# Run on connected device/emulator
flutter run
```

#### **On iOS** (macOS only)
```bash
# Open iOS Simulator
open -a Simulator

# Run on iOS
flutter run
```

#### **On Web**
```bash
flutter run -d chrome
```

#### **On Desktop**
```bash
# Windows
flutter run -d windows

# macOS
flutter run -d macos

# Linux
flutter run -d linux
```

## 🔧 **Development Setup**

### **VS Code Setup**
1. Install VS Code
2. Install Flutter extension
3. Install Dart extension
4. Open project folder
5. Press `F5` to run with debugging

### **Android Studio Setup**
1. Install Android Studio
2. Install Flutter plugin
3. Install Dart plugin
4. Open project
5. Click "Run" button

### **Recommended VS Code Extensions**
- Flutter
- Dart
- Flutter Widget Snippets
- Bracket Pair Colorizer
- GitLens
- Error Lens

## 📱 **Device Setup**

### **Android Device**
1. Enable Developer Options
2. Enable USB Debugging
3. Connect via USB
4. Accept debugging prompt

### **Android Emulator**
1. Open Android Studio
2. Go to AVD Manager
3. Create Virtual Device
4. Choose device and API level
5. Start emulator

### **iOS Simulator** (macOS only)
1. Open Xcode
2. Go to Xcode → Open Developer Tool → Simulator
3. Choose device from Hardware menu

## 🎨 **Customization Setup**

### **Changing App Name**
Edit `pubspec.yaml`:
```yaml
name: your_app_name
description: Your app description
```

### **Changing App Icon**
1. Replace `assets/icon/app_icon.png`
2. Run: `flutter pub run flutter_launcher_icons:main`

### **Changing Package Name**

#### **Android**
Edit `android/app/build.gradle`:
```gradle
android {
    defaultConfig {
        applicationId "com.yourcompany.yourapp"
    }
}
```

#### **iOS**
1. Open `ios/Runner.xcworkspace` in Xcode
2. Select Runner project
3. Change Bundle Identifier

### **Adding Custom Fonts**
1. Add font files to `assets/fonts/`
2. Update `pubspec.yaml`:
```yaml
flutter:
  fonts:
    - family: YourFont
      fonts:
        - asset: assets/fonts/YourFont-Regular.ttf
        - asset: assets/fonts/YourFont-Bold.ttf
          weight: 700
```

## 🌐 **Environment Configuration**

### **Development Environment**
Create `lib/config/dev_config.dart`:
```dart
class DevConfig {
  static const String apiBaseUrl = 'https://dev-api.yourapp.com';
  static const String appName = 'YourApp Dev';
  static const bool enableLogging = true;
}
```

### **Production Environment**
Create `lib/config/prod_config.dart`:
```dart
class ProdConfig {
  static const String apiBaseUrl = 'https://api.yourapp.com';
  static const String appName = 'YourApp';
  static const bool enableLogging = false;
}
```

## 🔌 **API Integration Setup**

### **Adding HTTP Package**
```yaml
dependencies:
  http: ^0.13.5
```

### **Creating API Service**
```dart
class ApiService {
  static const String baseUrl = 'https://your-api.com';
  
  static Future<List<Product>> getProducts() async {
    final response = await http.get(Uri.parse('$baseUrl/products'));
    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((json) => Product.fromJson(json)).toList();
    }
    throw Exception('Failed to load products');
  }
}
```

## 🗄️ **Database Setup**

### **Local Database (SQLite)**
Add dependency:
```yaml
dependencies:
  sqflite: ^2.2.8
  path: ^1.8.2
```

### **Firebase Setup**
1. Create Firebase project
2. Add Android/iOS apps
3. Download config files
4. Add Firebase dependencies:
```yaml
dependencies:
  firebase_core: ^2.10.0
  cloud_firestore: ^4.5.2
  firebase_auth: ^4.4.2
```

## 🧪 **Testing Setup**

### **Unit Tests**
Create `test/unit_test.dart`:
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:ecommerce_app/shared/models/product.dart';

void main() {
  group('Product Tests', () {
    test('should calculate discount correctly', () {
      final product = Product(
        id: '1',
        name: 'Test Product',
        price: 80.0,
        originalPrice: 100.0,
        // ... other required fields
      );
      
      expect(product.discountPercentage, 20.0);
    });
  });
}
```

### **Widget Tests**
Create `test/widget_test.dart`:
```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ecommerce_app/shared/widgets/product_card.dart';

void main() {
  testWidgets('ProductCard displays product name', (tester) async {
    final product = Product(/* mock product data */);
    
    await tester.pumpWidget(
      MaterialApp(
        home: ProductCard(product: product),
      ),
    );
    
    expect(find.text(product.name), findsOneWidget);
  });
}
```

### **Running Tests**
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/unit_test.dart

# Run with coverage
flutter test --coverage
```

## 🚀 **Build & Deployment**

### **Android APK**
```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release

# Split APKs by ABI
flutter build apk --split-per-abi
```

### **Android App Bundle**
```bash
flutter build appbundle --release
```

### **iOS Build**
```bash
# Debug
flutter build ios --debug

# Release
flutter build ios --release
```

### **Web Build**
```bash
flutter build web --release
```

### **Desktop Builds**
```bash
# Windows
flutter build windows --release

# macOS
flutter build macos --release

# Linux
flutter build linux --release
```

## 🔍 **Troubleshooting**

### **Common Issues**

#### **"Flutter command not found"**
- Add Flutter to your PATH
- Restart terminal/IDE
- Run `flutter doctor`

#### **"No connected devices"**
- Enable USB debugging (Android)
- Start emulator/simulator
- Check device connection

#### **"Gradle build failed"**
- Clean project: `flutter clean`
- Get dependencies: `flutter pub get`
- Check Android SDK installation

#### **"CocoaPods not installed"**
```bash
sudo gem install cocoapods
cd ios && pod install
```

#### **"Web renderer issues"**
```bash
flutter run -d chrome --web-renderer html
```

### **Performance Issues**
- Use `flutter run --profile` for performance testing
- Enable performance overlay: `flutter run --profile --trace-skia`
- Check memory usage in DevTools

## 📞 **Support**

### **Getting Help**
- 📖 [Flutter Documentation](https://flutter.dev/docs)
- 💬 [Flutter Community](https://flutter.dev/community)
- 🐛 [GitHub Issues](https://github.com/flutter/flutter/issues)
- 📧 Email: <EMAIL>

### **Useful Commands**
```bash
# Check Flutter installation
flutter doctor -v

# Clean project
flutter clean

# Update dependencies
flutter pub upgrade

# Analyze code
flutter analyze

# Format code
flutter format .

# Check for outdated packages
flutter pub outdated
```

---

**You're all set! 🎉 Happy coding with Flutter!**
